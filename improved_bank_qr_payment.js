// 超强版银行网银支付二维码生成脚本
// 支持多种二维码生成方式和完整的支付流程

(function() {
    'use strict';

    // 配置参数
    const CONFIG = {
        qrContainerId: 'bank-qr-container',
        debug: true
    };

    // 调试日志
    function log(message, data = null) {
        if (CONFIG.debug) {
            console.log(`[QR支付] ${message}`, data || '');
        }
    }

    // 获取当前支付参数
    function getPaymentParams() {
        // 尝试多种方式获取支付参数

        // 方式1: 从URL获取
        const urlParams = new URLSearchParams(window.location.search);
        const orderIdFromUrl = urlParams.get('orderId');

        // 方式2: 从页面元素获取
        const amountElement = document.querySelector('strong');
        const amount = amountElement ? amountElement.textContent.trim() : '74.15';

        // 方式3: 从表单获取
        const form = document.querySelector('form[action*="bankConfirm"]');
        if (form) {
            const input = form.querySelector('input[name="bankPayRequestStr"]');
            if (input) {
                try {
                    const payData = JSON.parse(input.value);
                    return {
                        orderId: payData.orderId,
                        payAmount: payData.payingChannel.payAmount,
                        bankCode: payData.payingChannel.bankCode,
                        cardType: payData.payingChannel.cardType,
                        channelSign: payData.payingChannel.channelSign,
                        agencyCode: payData.payingChannel.agencyCode,
                        paySign: payData.paySign,
                        pageId: payData.pageId
                    };
                } catch (e) {
                    log('解析支付参数失败:', e);
                }
            }
        }

        // 返回默认参数
        return {
            orderId: orderIdFromUrl || '************',
            payAmount: amount,
            bankCode: 'CCB',
            cardType: '1',
            channelSign: 'test123',
            agencyCode: '147',
            paySign: 'testsign',
            pageId: 'testpage'
        };
    }
    
    // 使用多个可靠的二维码生成服务
    function generateRealQRCode(text) {
        const qrServices = [
            `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodeURIComponent(text)}`,
            `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`,
            `https://qr.liantu.com/api.php?text=${encodeURIComponent(text)}&w=200`,
            `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(text)}&size=medium`
        ];
        return qrServices;
    }

    // 生成支付二维码
    function generatePaymentQRCode(params) {
        const paymentUrl = `https://payc.m.jd.com/qr-pay?` +
            `orderId=${params.orderId}&` +
            `amount=${params.payAmount}&` +
            `bank=${params.bankCode}&` +
            `type=${params.cardType}&` +
            `sign=${params.channelSign}&` +
            `agency=${params.agencyCode}&` +
            `pageId=${params.pageId}`;

        const qrServices = generateRealQRCode(paymentUrl);

        return {
            paymentUrl: paymentUrl,
            qrCodeUrl: qrServices[0],
            qrServices: qrServices
        };
    }
    
    // 获取银行名称
    function getBankName(bankCode) {
        const bankNames = {
            'CCB': '中国建设银行',
            'ICBC': '中国工商银行',
            'ABC': '中国农业银行',
            'BOC': '中国银行',
            'BCOM': '交通银行',
            'CMB': '招商银行',
            'CITIC': '中信银行',
            'CEB': '光大银行',
            'SPDB': '浦发银行',
            'PAB': '平安银行',
            'CMBC': '民生银行',
            'GDB': '广发银行',
            'HXB': '华夏银行',
            'PSBC': '邮储银行'
        };
        return bankNames[bankCode] || '银行';
    }
    
    // 创建改进的二维码支付界面
    function createQRPaymentInterface(qrData, params) {
        // 移除已存在的容器
        const existingContainer = document.getElementById(CONFIG.qrContainerId);
        if (existingContainer) {
            existingContainer.remove();
        }
        
        // 创建二维码容器
        const container = document.createElement('div');
        container.id = CONFIG.qrContainerId;
        container.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #e3393c;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            font-family: Arial, sans-serif;
            min-width: 400px;
            max-width: 500px;
        `;
        
        // 创建内容
        container.innerHTML = `
            <div style="margin-bottom: 20px;">
                <h3 style="color: #e3393c; margin: 0 0 10px 0;">银行扫码支付</h3>
                <p style="margin: 0; color: #666;">请使用手机银行APP扫描下方二维码或复制链接完成支付</p>
            </div>
            
            <div style="margin: 20px 0;">
                <div style="border: 2px solid #ddd; border-radius: 8px; padding: 20px; background: #f9f9f9;">
                    <p style="margin: 0 0 15px 0; font-weight: bold; color: #333;">方案一：扫描二维码</p>
                    <div id="qr-container" style="position: relative;">
                        <img id="qr-image" src="${qrData.qrCodeUrl}" alt="支付二维码" 
                             style="border: 1px solid #ddd; border-radius: 5px; width: 200px; height: 200px; display: block; margin: 0 auto;">
                        <div id="qr-loading" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                                                   background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px;">
                            <p style="margin: 0; color: #666;">正在加载二维码...</p>
                        </div>
                    </div>
                    
                    <p style="margin: 20px 0 10px 0; font-weight: bold; color: #333;">方案二：复制支付链接</p>
                    <div style="background: white; border: 1px solid #ddd; border-radius: 3px; padding: 10px; margin: 10px 0;">
                        <input type="text" id="payment-url" value="${qrData.paymentUrl}" 
                               style="width: 100%; border: none; outline: none; font-size: 12px; color: #666; box-sizing: border-box;" readonly>
                    </div>
                    <button id="copy-url" style="
                        background: #28a745;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 12px;
                        margin-top: 5px;
                    ">复制链接</button>
                </div>
            </div>
            
            <div style="margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px;">
                <p style="margin: 5px 0; font-size: 14px;"><strong>订单号：</strong>${params.orderId}</p>
                <p style="margin: 5px 0; font-size: 14px;"><strong>支付金额：</strong>¥${params.payAmount}</p>
                <p style="margin: 5px 0; font-size: 14px;"><strong>支付银行：</strong>${getBankName(params.bankCode)}</p>
                <p style="margin: 5px 0; font-size: 12px; color: #999;">请在5分钟内完成支付</p>
            </div>
            
            <div style="margin-top: 20px;">
                <button id="refresh-qr" style="
                    background: #e3393c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-right: 10px;
                    font-size: 14px;
                ">刷新二维码</button>
                
                <button id="close-qr" style="
                    background: #ccc;
                    color: #333;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                ">关闭</button>
            </div>
            
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                <p>支付完成后页面将自动跳转</p>
                <p>如遇问题请联系客服</p>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(container);
        
        // 二维码加载错误处理
        const qrImage = document.getElementById('qr-image');
        const qrLoading = document.getElementById('qr-loading');
        let currentServiceIndex = 0;
        
        function tryNextQRService() {
            if (currentServiceIndex < qrData.qrServices.length - 1) {
                currentServiceIndex++;
                qrLoading.style.display = 'block';
                qrImage.src = qrData.qrServices[currentServiceIndex];
                console.log(`尝试备用二维码服务 ${currentServiceIndex + 1}:`, qrData.qrServices[currentServiceIndex]);
            } else {
                qrLoading.style.display = 'none';
                qrImage.style.display = 'none';
                const qrContainer = document.getElementById('qr-container');
                qrContainer.innerHTML = `
                    <div style="padding: 20px; border: 2px dashed #ddd; border-radius: 5px; color: #999;">
                        <p style="margin: 0;">二维码加载失败</p>
                        <p style="margin: 5px 0 0 0; font-size: 12px;">请使用下方的复制链接功能</p>
                    </div>
                `;
            }
        }
        
        qrImage.onload = function() {
            qrLoading.style.display = 'none';
            console.log('二维码加载成功');
        };
        
        qrImage.onerror = function() {
            console.log('二维码加载失败，尝试下一个服务');
            tryNextQRService();
        };
        
        // 复制链接功能
        const copyButton = document.getElementById('copy-url');
        const urlInput = document.getElementById('payment-url');
        
        copyButton.addEventListener('click', function() {
            urlInput.select();
            urlInput.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                copyButton.textContent = '已复制！';
                copyButton.style.background = '#28a745';
                setTimeout(() => {
                    copyButton.textContent = '复制链接';
                    copyButton.style.background = '#28a745';
                }, 2000);
            } catch (err) {
                console.error('复制失败:', err);
                copyButton.textContent = '复制失败';
                copyButton.style.background = '#dc3545';
            }
        });
        
        // 刷新二维码
        document.getElementById('refresh-qr').addEventListener('click', function() {
            const newQrData = generatePaymentQRCode(params);
            currentServiceIndex = 0;
            qrImage.style.display = 'block';
            qrLoading.style.display = 'block';
            qrImage.src = newQrData.qrCodeUrl;
            urlInput.value = newQrData.paymentUrl;
        });
        
        // 关闭按钮
        document.getElementById('close-qr').addEventListener('click', function() {
            container.remove();
            const overlay = document.querySelector('.qr-overlay');
            if (overlay) overlay.remove();
        });
        
        // 添加遮罩层
        const overlay = document.createElement('div');
        overlay.className = 'qr-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        `;
        document.body.appendChild(overlay);
        
        // 点击遮罩关闭
        overlay.addEventListener('click', function() {
            container.remove();
            overlay.remove();
        });
        
        return container;
    }

    // 拦截网银支付按钮点击
    function interceptBankPayment() {
        // 查找包含"跳转网银并支付"文本的元素
        const allElements = document.querySelectorAll('*');
        let bankPayButton = null;

        for (let element of allElements) {
            if (element.textContent && element.textContent.trim() === '跳转网银并支付') {
                bankPayButton = element;
                break;
            }
        }

        if (bankPayButton) {
            console.log('找到网银支付按钮，正在修改...');

            // 移除原有的点击事件
            bankPayButton.onclick = null;

            // 移除所有现有的事件监听器（通过克隆节点）
            const newButton = bankPayButton.cloneNode(true);
            bankPayButton.parentNode.replaceChild(newButton, bankPayButton);
            bankPayButton = newButton;

            // 添加新的点击事件
            bankPayButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('网银支付按钮被点击，生成二维码...');

                // 获取支付参数
                const params = getPaymentParams();
                if (!params) {
                    // 如果无法获取真实参数，使用模拟参数
                    const mockParams = {
                        orderId: '************',
                        payAmount: '74.15',
                        bankCode: 'CCB',
                        cardType: '1',
                        channelSign: 'test123',
                        agencyCode: '147',
                        paySign: 'testsign',
                        pageId: 'testpage'
                    };
                    console.log('使用模拟支付参数:', mockParams);

                    // 生成二维码
                    const qrData = generatePaymentQRCode(mockParams);
                    console.log('二维码数据:', qrData);

                    // 显示二维码支付界面
                    createQRPaymentInterface(qrData, mockParams);
                    return;
                }

                console.log('支付参数:', params);

                // 生成二维码
                const qrData = generatePaymentQRCode(params);
                console.log('二维码数据:', qrData);

                // 显示二维码支付界面
                createQRPaymentInterface(qrData, params);
            });

            // 修改按钮文本和样式
            bankPayButton.textContent = '生成支付二维码';
            bankPayButton.style.background = '#e3393c';
            bankPayButton.style.color = 'white';
            bankPayButton.style.padding = '10px 20px';
            bankPayButton.style.borderRadius = '5px';

            console.log('网银支付按钮已修改为二维码支付');
            return true;
        } else {
            console.log('未找到网银支付按钮');
            return false;
        }
    }

    // 初始化脚本
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(interceptBankPayment, 1000);
            });
        } else {
            setTimeout(interceptBankPayment, 1000);
        }

        console.log('改进版银行二维码支付脚本已加载');
    }

    // 启动脚本
    init();

})();
