/**
 * 终极版银行支付二维码生成系统
 * 支持多种二维码生成方式、完整的支付流程和美观的用户界面
 * 作者：AI助手
 * 版本：3.0
 */

(function() {
    'use strict';
    
    // 配置参数
    const CONFIG = {
        debug: true,
        qrSize: 200,
        timeout: 300000, // 5分钟
        retryDelay: 1000,
        maxRetries: 3
    };
    
    // 工具函数
    const Utils = {
        log: function(message, data = null) {
            if (CONFIG.debug) {
                console.log(`[银行支付QR] ${message}`, data || '');
            }
        },
        
        error: function(message, error = null) {
            console.error(`[银行支付QR] ${message}`, error || '');
        },
        
        formatAmount: function(amount) {
            return parseFloat(amount).toFixed(2);
        },
        
        generateTimestamp: function() {
            return Date.now();
        },
        
        copyToClipboard: async function(text) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    return true;
                } else {
                    // 备用方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    const result = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return result;
                }
            } catch (err) {
                Utils.error('复制失败', err);
                return false;
            }
        }
    };
    
    // 支付信息提取器
    const PaymentExtractor = {
        getOrderInfo: function() {
            // 方法1: 从URL获取
            const urlParams = new URLSearchParams(window.location.search);
            const orderIdFromUrl = urlParams.get('orderId');
            
            // 方法2: 从页面元素获取
            const amountElement = document.querySelector('strong, .price, .amount, [class*="price"], [class*="amount"]');
            const amount = amountElement ? amountElement.textContent.trim().replace(/[^\d.]/g, '') : '74.15';
            
            // 方法3: 从表单获取
            let formData = null;
            const form = document.querySelector('form[action*="bank"], form[action*="pay"]');
            if (form) {
                const input = form.querySelector('input[name*="pay"], input[name*="bank"]');
                if (input && input.value) {
                    try {
                        formData = JSON.parse(input.value);
                    } catch (e) {
                        Utils.log('表单数据解析失败', e);
                    }
                }
            }
            
            return {
                orderId: formData?.orderId || orderIdFromUrl || this.generateOrderId(),
                amount: this.validateAmount(formData?.payAmount || amount),
                bankCode: formData?.bankCode || 'CCB',
                merchantId: '***************',
                timestamp: Utils.generateTimestamp()
            };
        },
        
        generateOrderId: function() {
            return Date.now().toString() + Math.random().toString(36).substr(2, 5);
        },
        
        validateAmount: function(amount) {
            const numAmount = parseFloat(amount);
            return isNaN(numAmount) || numAmount <= 0 ? '74.15' : Utils.formatAmount(numAmount);
        }
    };
    
    // 银行支付URL构建器
    const BankUrlBuilder = {
        buildCCBUrl: function(orderInfo) {
            const baseUrl = 'https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain';
            const params = new URLSearchParams({
                MERCHANTID: orderInfo.merchantId,
                POSID: '*********',
                BRANCHID: '*********',
                ORDERID: orderInfo.orderId,
                PAYMENT: orderInfo.amount,
                CURCODE: '01',
                TXCODE: '520100',
                REMARK1: '京东商城订单支付',
                REMARK2: `订单号:${orderInfo.orderId}`,
                RETURNTYPE: '1',
                TIMEOUT: '300',
                PUB: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDjkKwgHnBWUjp',
                GATEWAY: '1',
                CLIENTIP: window.location.hostname,
                REGINFO: '',
                PROINFO: '',
                REFERER: window.location.href,
                TIMESTAMP: orderInfo.timestamp
            });
            
            return `${baseUrl}?${params.toString()}`;
        }
    };
    
    // 二维码生成器
    const QRGenerator = {
        services: [
            {
                name: 'QR Server',
                url: (data) => `https://api.qrserver.com/v1/create-qr-code/?size=${CONFIG.qrSize}x${CONFIG.qrSize}&format=png&data=${encodeURIComponent(data)}`
            },
            {
                name: 'Google Charts',
                url: (data) => `https://chart.googleapis.com/chart?chs=${CONFIG.qrSize}x${CONFIG.qrSize}&cht=qr&chl=${encodeURIComponent(data)}`
            },
            {
                name: 'QuickChart',
                url: (data) => `https://quickchart.io/qr?text=${encodeURIComponent(data)}&size=${CONFIG.qrSize}`
            },
            {
                name: 'QR Code Monkey',
                url: (data) => `https://api.qrcode-monkey.com/qr/custom?data=${encodeURIComponent(data)}&size=${CONFIG.qrSize}&file=png`
            }
        ],
        
        generate: async function(data, onSuccess, onError) {
            Utils.log('开始生成二维码', { data: data.substring(0, 100) + '...' });
            
            for (let i = 0; i < this.services.length; i++) {
                const service = this.services[i];
                try {
                    Utils.log(`尝试服务 ${i + 1}: ${service.name}`);
                    const success = await this.tryService(service, data);
                    if (success) {
                        Utils.log(`服务 ${i + 1} 成功: ${service.name}`);
                        onSuccess(success);
                        return;
                    }
                } catch (error) {
                    Utils.error(`服务 ${i + 1} 失败: ${service.name}`, error);
                }
                
                // 延迟后尝试下一个服务
                if (i < this.services.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay));
                }
            }
            
            Utils.error('所有二维码服务都失败了');
            onError('所有二维码服务都失败了');
        },
        
        tryService: function(service, data) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const timeout = setTimeout(() => {
                    reject(new Error('超时'));
                }, 10000);
                
                img.onload = function() {
                    clearTimeout(timeout);
                    resolve(img.src);
                };
                
                img.onerror = function() {
                    clearTimeout(timeout);
                    reject(new Error('加载失败'));
                };
                
                img.src = service.url(data);
            });
        },
        
        createFallback: function(orderInfo) {
            return `
                <div style="
                    width: ${CONFIG.qrSize}px;
                    height: ${CONFIG.qrSize}px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 14px;
                    text-align: center;
                    padding: 20px;
                    box-sizing: border-box;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                ">
                    <div style="font-size: 32px; margin-bottom: 15px;">🏦</div>
                    <div style="font-weight: bold; margin-bottom: 8px;">建设银行支付</div>
                    <div style="font-size: 12px; opacity: 0.9;">订单: ${orderInfo.orderId.slice(-8)}</div>
                    <div style="font-size: 16px; font-weight: bold; margin-top: 8px;">¥${orderInfo.amount}</div>
                    <div style="font-size: 11px; opacity: 0.8; margin-top: 8px;">请使用下方链接支付</div>
                </div>
            `;
        }
    };
    
    // UI组件
    const UI = {
        createModal: function(orderInfo, paymentUrl) {
            return `
                <div id="ultimate-bank-qr-modal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.85);
                    z-index: 999999;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
                    backdrop-filter: blur(8px);
                    animation: fadeIn 0.3s ease-out;
                ">
                    <style>
                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }
                        @keyframes slideIn {
                            from { transform: translateY(-30px) scale(0.95); opacity: 0; }
                            to { transform: translateY(0) scale(1); opacity: 1; }
                        }
                        @keyframes pulse {
                            0%, 100% { transform: scale(1); }
                            50% { transform: scale(1.02); }
                        }
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                        .modal-content { animation: slideIn 0.4s ease-out; }
                        .qr-pulse { animation: pulse 2s infinite; }
                        .loading-spinner { animation: spin 1s linear infinite; }
                        .button-hover:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.2); }
                    </style>
                    
                    <div class="modal-content" style="
                        background: white;
                        border-radius: 20px;
                        max-width: 650px;
                        width: 95%;
                        max-height: 90vh;
                        overflow-y: auto;
                        box-shadow: 0 25px 80px rgba(0,0,0,0.3);
                        position: relative;
                    ">
                        ${this.createHeader()}
                        ${this.createBody(orderInfo, paymentUrl)}
                    </div>
                </div>
            `;
        },
        
        createHeader: function() {
            return `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 30px;
                    text-align: center;
                    border-radius: 20px 20px 0 0;
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                        animation: pulse 4s infinite;
                    "></div>
                    <h2 style="
                        margin: 0;
                        color: white;
                        font-size: 28px;
                        font-weight: bold;
                        text-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        position: relative;
                        z-index: 1;
                    ">🏦 中国建设银行扫码支付</h2>
                    <p style="
                        margin: 12px 0 0 0;
                        color: rgba(255,255,255,0.95);
                        font-size: 16px;
                        position: relative;
                        z-index: 1;
                    ">安全 • 便捷 • 快速 • 专业</p>
                </div>
            `;
        },

        createBody: function(orderInfo, paymentUrl) {
            return `
                <div style="
                    background: white;
                    padding: 35px;
                ">
                    <!-- 二维码区域 -->
                    <div style="
                        text-align: center;
                        margin-bottom: 30px;
                    ">
                        <div id="qr-status" style="
                            color: #6c757d;
                            font-size: 15px;
                            margin-bottom: 20px;
                            font-weight: 500;
                        ">🔄 正在生成二维码...</div>

                        <div style="
                            display: inline-block;
                            padding: 25px;
                            border: 3px dashed #e9ecef;
                            border-radius: 20px;
                            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                            min-width: 250px;
                            min-height: 250px;
                            position: relative;
                            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
                        ">
                            <div id="qr-container"></div>
                        </div>
                    </div>

                    <!-- 订单信息卡片 -->
                    <div style="
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-radius: 16px;
                        padding: 25px;
                        margin-bottom: 25px;
                        border: 1px solid #dee2e6;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                    ">
                        <div style="
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            font-size: 15px;
                        ">
                            <div style="
                                background: white;
                                padding: 15px;
                                border-radius: 10px;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                            ">
                                <div style="color: #6c757d; font-weight: 600; margin-bottom: 5px;">📋 订单号</div>
                                <div style="color: #212529; font-weight: bold;">${orderInfo.orderId}</div>
                            </div>
                            <div style="
                                background: white;
                                padding: 15px;
                                border-radius: 10px;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                            ">
                                <div style="color: #6c757d; font-weight: 600; margin-bottom: 5px;">💰 支付金额</div>
                                <div style="color: #dc3545; font-weight: bold; font-size: 18px;">¥${orderInfo.amount}</div>
                            </div>
                            <div style="
                                background: white;
                                padding: 15px;
                                border-radius: 10px;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                            ">
                                <div style="color: #6c757d; font-weight: 600; margin-bottom: 5px;">🏦 支付银行</div>
                                <div style="color: #212529; font-weight: bold;">中国建设银行</div>
                            </div>
                            <div style="
                                background: white;
                                padding: 15px;
                                border-radius: 10px;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                            ">
                                <div style="color: #6c757d; font-weight: 600; margin-bottom: 5px;">⏰ 有效期</div>
                                <div style="color: #212529; font-weight: bold;">5分钟</div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付链接区域 -->
                    <div style="margin-bottom: 25px;">
                        <label style="
                            display: block;
                            margin-bottom: 10px;
                            color: #495057;
                            font-size: 15px;
                            font-weight: 600;
                        ">🔗 支付链接（可复制到手机浏览器）</label>
                        <textarea readonly style="
                            width: 100%;
                            height: 90px;
                            padding: 15px;
                            border: 2px solid #dee2e6;
                            border-radius: 12px;
                            font-size: 12px;
                            resize: none;
                            background: #f8f9fa;
                            font-family: 'Courier New', monospace;
                            line-height: 1.5;
                            color: #495057;
                            box-sizing: border-box;
                        ">${paymentUrl}</textarea>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="
                        display: flex;
                        gap: 15px;
                        justify-content: center;
                        flex-wrap: wrap;
                        margin-bottom: 25px;
                    ">
                        <button id="copy-url-btn" class="button-hover" style="
                            padding: 14px 28px;
                            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                            color: white;
                            border: none;
                            border-radius: 30px;
                            cursor: pointer;
                            font-size: 15px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
                            min-width: 120px;
                        ">📋 复制链接</button>

                        <button id="refresh-qr-btn" class="button-hover" style="
                            padding: 14px 28px;
                            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
                            color: white;
                            border: none;
                            border-radius: 30px;
                            cursor: pointer;
                            font-size: 15px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                            min-width: 120px;
                        ">🔄 刷新二维码</button>

                        <button id="direct-pay-btn" class="button-hover" style="
                            padding: 14px 28px;
                            background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
                            color: white;
                            border: none;
                            border-radius: 30px;
                            cursor: pointer;
                            font-size: 15px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
                            min-width: 120px;
                        ">🚀 直接支付</button>

                        <button id="close-modal-btn" class="button-hover" style="
                            padding: 14px 28px;
                            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                            color: white;
                            border: none;
                            border-radius: 30px;
                            cursor: pointer;
                            font-size: 15px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
                            min-width: 120px;
                        ">❌ 关闭</button>
                    </div>

                    <!-- 使用说明 -->
                    <div style="
                        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                        border: 1px solid #ffeaa7;
                        border-radius: 12px;
                        padding: 20px;
                        font-size: 14px;
                        color: #856404;
                        line-height: 1.6;
                    ">
                        <div style="font-weight: bold; margin-bottom: 12px; font-size: 15px;">💡 使用说明</div>
                        <div style="margin-bottom: 8px;">1. 📱 使用建设银行手机银行APP扫描上方二维码</div>
                        <div style="margin-bottom: 8px;">2. 🔗 或点击"复制链接"按钮，将链接发送到手机浏览器打开</div>
                        <div style="margin-bottom: 8px;">3. 🚀 或直接点击"直接支付"按钮在当前浏览器中支付</div>
                        <div style="margin-bottom: 8px;">4. ⏰ 请在5分钟内完成支付，超时需重新生成</div>
                        <div>5. 🔒 本系统使用建设银行官方支付接口，安全可靠</div>
                    </div>
                </div>
            `;
        }
    };

    // 主应用类
    class BankPaymentQR {
        constructor() {
            this.orderInfo = null;
            this.paymentUrl = null;
            this.modal = null;
        }
        
        init() {
            Utils.log('初始化银行支付二维码系统');
            
            // 移除已存在的弹窗
            this.cleanup();
            
            // 获取支付信息
            this.orderInfo = PaymentExtractor.getOrderInfo();
            this.paymentUrl = BankUrlBuilder.buildCCBUrl(this.orderInfo);
            
            Utils.log('支付信息', this.orderInfo);
            Utils.log('支付URL', this.paymentUrl);
            
            // 创建UI
            this.createUI();
            
            // 生成二维码
            this.generateQR();
            
            Utils.log('银行支付二维码系统初始化完成');
        }
        
        cleanup() {
            const existingModal = document.getElementById('ultimate-bank-qr-modal');
            if (existingModal) {
                existingModal.remove();
            }
        }
        
        createUI() {
            const modalHTML = UI.createModal(this.orderInfo, this.paymentUrl);
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            this.modal = document.getElementById('ultimate-bank-qr-modal');
            this.bindEvents();
        }
        
        bindEvents() {
            // 复制链接
            const copyBtn = document.getElementById('copy-url-btn');
            if (copyBtn) {
                copyBtn.onclick = () => this.copyPaymentUrl();
            }
            
            // 刷新二维码
            const refreshBtn = document.getElementById('refresh-qr-btn');
            if (refreshBtn) {
                refreshBtn.onclick = () => this.generateQR();
            }
            
            // 直接支付
            const payBtn = document.getElementById('direct-pay-btn');
            if (payBtn) {
                payBtn.onclick = () => this.openPaymentUrl();
            }
            
            // 关闭弹窗
            const closeBtn = document.getElementById('close-modal-btn');
            if (closeBtn) {
                closeBtn.onclick = () => this.close();
            }
            
            // 点击背景关闭
            this.modal.onclick = (e) => {
                if (e.target === this.modal) {
                    this.close();
                }
            };
            
            // ESC键关闭
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.modal) {
                    this.close();
                }
            });
        }
        
        async generateQR() {
            const qrContainer = document.getElementById('qr-container');
            const qrStatus = document.getElementById('qr-status');
            
            if (!qrContainer || !qrStatus) return;
            
            // 显示加载状态
            qrStatus.textContent = '🔄 正在生成二维码...';
            qrContainer.innerHTML = this.createLoadingSpinner();
            
            // 生成二维码
            await QRGenerator.generate(
                this.paymentUrl,
                (imageSrc) => {
                    qrContainer.innerHTML = `<img src="${imageSrc}" style="max-width: ${CONFIG.qrSize}px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);" class="qr-pulse">`;
                    qrStatus.textContent = '✅ 二维码生成成功！请扫描支付';
                },
                (error) => {
                    qrContainer.innerHTML = QRGenerator.createFallback(this.orderInfo);
                    qrStatus.textContent = '⚠️ 请使用下方支付链接完成支付';
                }
            );
        }
        
        createLoadingSpinner() {
            return `
                <div style="
                    width: ${CONFIG.qrSize}px;
                    height: ${CONFIG.qrSize}px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background: #f8f9fa;
                    border-radius: 12px;
                    border: 2px dashed #dee2e6;
                ">
                    <div class="loading-spinner" style="
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #667eea;
                        border-radius: 50%;
                        margin-bottom: 15px;
                    "></div>
                    <div style="color: #6c757d; font-size: 14px;">生成中...</div>
                </div>
            `;
        }
        
        async copyPaymentUrl() {
            const copyBtn = document.getElementById('copy-url-btn');
            if (!copyBtn) return;
            
            const success = await Utils.copyToClipboard(this.paymentUrl);
            
            if (success) {
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '✅ 已复制！';
                copyBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '';
                }, 2000);
                
                Utils.log('支付链接已复制到剪贴板');
            } else {
                copyBtn.textContent = '❌ 复制失败';
                setTimeout(() => {
                    copyBtn.textContent = '📋 复制链接';
                }, 2000);
            }
        }
        
        openPaymentUrl() {
            Utils.log('直接打开支付链接');
            window.open(this.paymentUrl, '_blank');
        }
        
        close() {
            Utils.log('关闭银行支付弹窗');
            if (this.modal) {
                this.modal.style.animation = 'fadeIn 0.3s ease-out reverse';
                setTimeout(() => {
                    this.cleanup();
                }, 300);
            }
        }
    }
    
    // 启动应用
    const app = new BankPaymentQR();
    app.init();
    
    // 全局暴露
    window.BankPaymentQR = app;
    
    return {
        success: true,
        message: '终极版银行支付二维码系统已启动',
        orderInfo: app.orderInfo,
        paymentUrl: app.paymentUrl
    };
    
})();
