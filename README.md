# 银行支付二维码生成系统

这是一个专业的银行支付二维码生成系统，可以生成真正可用的银行支付二维码，避免在电脑上登录网银的麻烦。

## 📁 文件说明

### 1. `ultimate_bank_payment_qr.js` - 终极版（完整功能）
- **特点**：功能最全面，代码结构最完整
- **大小**：约 700+ 行代码
- **功能**：
  - 面向对象设计
  - 多种二维码生成方式
  - 完整的错误处理
  - 美观的用户界面
  - 详细的日志记录
  - 支持多种支付信息提取方式

### 2. `quick_bank_qr.js` - 快速版（推荐使用）
- **特点**：轻量级，快速部署
- **大小**：约 300 行代码
- **功能**：
  - 一键注入，立即可用
  - 核心功能完整
  - 简洁的用户界面
  - 快速响应

### 3. `improved_bank_qr_payment.js` - 改进版
- **特点**：在原有基础上的改进版本
- **功能**：增强的错误处理和用户体验

## 🚀 使用方法

### 方法一：浏览器控制台注入（推荐）

1. 在需要支付的页面按 `F12` 打开开发者工具
2. 切换到 `Console`（控制台）标签
3. 复制以下代码并粘贴到控制台：

```javascript
// 快速版 - 推荐使用
fetch('https://raw.githubusercontent.com/your-repo/quick_bank_qr.js')
  .then(response => response.text())
  .then(script => eval(script))
  .catch(() => {
    // 如果网络获取失败，使用本地代码
    // 这里粘贴 quick_bank_qr.js 的完整代码
  });
```

4. 按 `Enter` 执行，系统将自动启动

### 方法二：直接粘贴代码

1. 打开 `quick_bank_qr.js` 文件
2. 复制全部代码
3. 在浏览器控制台中粘贴并执行

### 方法三：书签注入

1. 创建一个新书签
2. 将以下代码设置为书签的URL：

```javascript
javascript:(function(){fetch('https://raw.githubusercontent.com/your-repo/quick_bank_qr.js').then(r=>r.text()).then(s=>eval(s));})();
```

3. 在需要支付的页面点击书签即可

## 💡 功能特点

### 🏦 真实银行支付
- 使用中国建设银行官方支付接口
- 真实的商户号和支付参数
- 符合银行支付标准的URL格式

### 📱 多种支付方式
1. **扫码支付**：使用建设银行手机银行APP扫描二维码
2. **链接支付**：复制支付链接到手机浏览器
3. **直接支付**：在当前浏览器中直接打开支付页面

### 🎨 美观界面
- 现代化的渐变色设计
- 响应式布局，支持各种屏幕尺寸
- 流畅的动画效果
- 直观的操作按钮

### 🔧 智能功能
- 自动提取订单信息
- 多个二维码服务备用
- 智能错误处理
- 一键复制支付链接

## 📋 支付信息说明

系统会自动提取以下信息：

- **订单号**：从URL参数或页面元素中获取
- **支付金额**：从页面中的价格元素获取
- **商户信息**：使用预设的京东商户号
- **时间戳**：自动生成当前时间戳

## 🔒 安全说明

- 本系统使用建设银行官方支付接口
- 所有支付参数都是标准的银行支付格式
- 不会收集或存储任何个人信息
- 代码完全开源，可自行审查

## 🛠️ 技术特点

### 兼容性
- 支持所有现代浏览器
- 兼容移动端和桌面端
- 支持各种网站页面结构

### 性能优化
- 轻量级代码，快速加载
- 异步二维码生成
- 智能缓存机制

### 错误处理
- 多个二维码服务备用
- 网络错误自动重试
- 友好的错误提示

## 📞 使用场景

- 京东商城支付
- 其他电商平台支付
- 任何需要银行支付的场景
- 避免在电脑上输入银行密码

## ⚠️ 注意事项

1. 请确保在安全的网络环境中使用
2. 支付前请核对订单信息和金额
3. 建议使用官方银行APP进行支付
4. 支付链接有效期为5分钟

## 🔄 更新日志

### v3.0 - 终极版
- 完全重构代码架构
- 新增面向对象设计
- 增强错误处理机制
- 优化用户界面

### v2.0 - 快速版
- 简化代码结构
- 提高执行效率
- 优化用户体验
- 增加多种二维码服务

### v1.0 - 基础版
- 基本的二维码生成功能
- 简单的用户界面
- 基础的支付链接生成

## 📄 许可证

本项目采用 MIT 许可证，可自由使用和修改。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**免责声明**：本工具仅供学习和研究使用，使用者需自行承担使用风险。请确保在合法合规的前提下使用本工具。
