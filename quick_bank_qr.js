/**
 * 快速银行支付二维码生成脚本
 * 一键注入，立即可用
 */

// 立即执行函数，避免全局污染
(function() {
    'use strict';
    
    console.log('🚀 启动快速银行支付二维码系统...');
    
    // 移除已存在的弹窗
    const existingModal = document.getElementById('quick-bank-qr-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 获取支付信息
    const urlParams = new URLSearchParams(window.location.search);
    const orderIdFromUrl = urlParams.get('orderId');
    const amountElement = document.querySelector('strong, .price, .amount');
    const amount = amountElement ? amountElement.textContent.trim().replace(/[^\d.]/g, '') : '74.15';
    
    const orderInfo = {
        orderId: orderIdFromUrl || Date.now().toString(),
        amount: amount || '74.15',
        timestamp: Date.now()
    };
    
    // 构建建设银行支付URL
    const paymentUrl = `https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain?` +
        `MERCHANTID=***************&` +
        `POSID=*********&` +
        `BRANCHID=*********&` +
        `ORDERID=${orderInfo.orderId}&` +
        `PAYMENT=${orderInfo.amount}&` +
        `CURCODE=01&` +
        `TXCODE=520100&` +
        `REMARK1=${encodeURIComponent('京东商城订单支付')}&` +
        `REMARK2=${encodeURIComponent('订单号:' + orderInfo.orderId)}&` +
        `RETURNTYPE=1&` +
        `TIMEOUT=300&` +
        `PUB=${encodeURIComponent('MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDjkKwgHnBWUjp')}&` +
        `GATEWAY=1&` +
        `CLIENTIP=${encodeURIComponent(window.location.hostname)}&` +
        `REGINFO=&` +
        `PROINFO=&` +
        `REFERER=${encodeURIComponent(window.location.href)}&` +
        `TIMESTAMP=${orderInfo.timestamp}`;
    
    console.log('💰 支付信息:', orderInfo);
    console.log('🔗 支付URL:', paymentUrl);
    
    // 创建弹窗HTML
    const modalHTML = `
        <div id="quick-bank-qr-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 999999;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            backdrop-filter: blur(5px);
            animation: fadeIn 0.3s ease-out;
        ">
            <style>
                @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
                @keyframes slideIn { from { transform: translateY(-20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
                @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.02); } }
                @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                .qr-pulse { animation: pulse 2s infinite; }
                .loading-spinner { animation: spin 1s linear infinite; }
            </style>
            
            <div style="
                background: white;
                border-radius: 20px;
                max-width: 600px;
                width: 95%;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                animation: slideIn 0.4s ease-out;
                overflow: hidden;
            ">
                <!-- 头部 -->
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 25px;
                    text-align: center;
                    color: white;
                ">
                    <h2 style="margin: 0; font-size: 24px; font-weight: bold;">🏦 中国建设银行扫码支付</h2>
                    <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.9;">安全 • 便捷 • 快速</p>
                </div>
                
                <!-- 主体内容 -->
                <div style="padding: 30px;">
                    <!-- 二维码区域 -->
                    <div style="text-align: center; margin-bottom: 25px;">
                        <div id="qr-status" style="color: #666; font-size: 14px; margin-bottom: 15px;">🔄 正在生成二维码...</div>
                        <div style="
                            display: inline-block;
                            padding: 20px;
                            border: 3px dashed #e0e0e0;
                            border-radius: 15px;
                            background: #f9f9f9;
                            min-width: 240px;
                            min-height: 240px;
                        ">
                            <div id="qr-container">
                                <div style="
                                    width: 200px;
                                    height: 200px;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    justify-content: center;
                                ">
                                    <div class="loading-spinner" style="
                                        width: 40px;
                                        height: 40px;
                                        border: 4px solid #f3f3f3;
                                        border-top: 4px solid #667eea;
                                        border-radius: 50%;
                                        margin-bottom: 15px;
                                    "></div>
                                    <div style="color: #999; font-size: 14px;">生成中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单信息 -->
                    <div style="
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        border-radius: 12px;
                        padding: 20px;
                        margin-bottom: 20px;
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 15px;
                        font-size: 14px;
                    ">
                        <div>
                            <span style="color: #666; font-weight: bold;">📋 订单号：</span>
                            <span style="color: #333;">${orderInfo.orderId}</span>
                        </div>
                        <div>
                            <span style="color: #666; font-weight: bold;">💰 支付金额：</span>
                            <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">¥${orderInfo.amount}</span>
                        </div>
                        <div>
                            <span style="color: #666; font-weight: bold;">🏦 支付银行：</span>
                            <span style="color: #333;">中国建设银行</span>
                        </div>
                        <div>
                            <span style="color: #666; font-weight: bold;">⏰ 有效期：</span>
                            <span style="color: #333;">5分钟</span>
                        </div>
                    </div>
                    
                    <!-- 支付链接 -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; color: #333; font-size: 14px; font-weight: bold;">
                            🔗 支付链接（可复制到手机浏览器）：
                        </label>
                        <textarea id="payment-url" readonly style="
                            width: 100%;
                            height: 80px;
                            padding: 12px;
                            border: 2px solid #e0e0e0;
                            border-radius: 8px;
                            font-size: 12px;
                            resize: none;
                            background: #f9f9f9;
                            font-family: monospace;
                            line-height: 1.4;
                            box-sizing: border-box;
                        ">${paymentUrl}</textarea>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                        <button id="copy-url-btn" style="
                            padding: 12px 24px;
                            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
                        ">📋 复制链接</button>
                        
                        <button id="refresh-qr-btn" style="
                            padding: 12px 24px;
                            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                        ">🔄 刷新二维码</button>
                        
                        <button id="direct-pay-btn" style="
                            padding: 12px 24px;
                            background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
                        ">🚀 直接支付</button>
                        
                        <button id="close-modal-btn" style="
                            padding: 12px 24px;
                            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
                        ">❌ 关闭</button>
                    </div>
                    
                    <!-- 使用说明 -->
                    <div style="
                        background: #fff3cd;
                        border: 1px solid #ffeaa7;
                        border-radius: 8px;
                        padding: 15px;
                        font-size: 13px;
                        color: #856404;
                    ">
                        <div style="font-weight: bold; margin-bottom: 8px;">💡 使用说明：</div>
                        <div>1. 📱 使用建设银行手机银行APP扫描二维码</div>
                        <div>2. 🔗 或复制链接到手机浏览器打开</div>
                        <div>3. 🚀 或直接点击"直接支付"按钮</div>
                        <div>4. ⏰ 请在5分钟内完成支付</div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 插入弹窗
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 二维码生成函数
    function generateQRCode() {
        const qrContainer = document.getElementById('qr-container');
        const qrStatus = document.getElementById('qr-status');
        
        console.log('🎯 开始生成二维码...');
        
        // 二维码服务列表
        const qrServices = [
            `https://api.qrserver.com/v1/create-qr-code/?size=200x200&format=png&data=${encodeURIComponent(paymentUrl)}`,
            `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodeURIComponent(paymentUrl)}`,
            `https://quickchart.io/qr?text=${encodeURIComponent(paymentUrl)}&size=200`
        ];
        
        let serviceIndex = 0;
        
        function tryNextService() {
            if (serviceIndex >= qrServices.length) {
                console.log('❌ 所有服务失败，显示备用方案');
                qrContainer.innerHTML = `
                    <div style="
                        width: 200px;
                        height: 200px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 12px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 14px;
                        text-align: center;
                        padding: 20px;
                        box-sizing: border-box;
                    ">
                        <div style="font-size: 32px; margin-bottom: 15px;">🏦</div>
                        <div style="font-weight: bold; margin-bottom: 8px;">建设银行支付</div>
                        <div style="font-size: 12px; opacity: 0.9;">订单: ${orderInfo.orderId.slice(-8)}</div>
                        <div style="font-size: 16px; font-weight: bold; margin-top: 8px;">¥${orderInfo.amount}</div>
                        <div style="font-size: 11px; opacity: 0.8; margin-top: 8px;">请使用下方链接支付</div>
                    </div>
                `;
                qrStatus.textContent = '⚠️ 请使用下方支付链接完成支付';
                return;
            }
            
            console.log(`🔄 尝试服务 ${serviceIndex + 1}`);
            
            const img = new Image();
            img.onload = function() {
                console.log(`✅ 服务 ${serviceIndex + 1} 成功`);
                qrContainer.innerHTML = `<img src="${img.src}" style="max-width: 200px; border-radius: 8px;" class="qr-pulse">`;
                qrStatus.textContent = '✅ 二维码生成成功！请扫描支付';
            };
            img.onerror = function() {
                console.log(`❌ 服务 ${serviceIndex + 1} 失败`);
                serviceIndex++;
                setTimeout(tryNextService, 1000);
            };
            img.src = qrServices[serviceIndex];
        }
        
        tryNextService();
    }
    
    // 复制链接函数
    async function copyPaymentUrl() {
        const textarea = document.getElementById('payment-url');
        const copyBtn = document.getElementById('copy-url-btn');
        
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(paymentUrl);
            } else {
                textarea.select();
                textarea.setSelectionRange(0, 99999);
                document.execCommand('copy');
            }
            
            copyBtn.textContent = '✅ 已复制！';
            copyBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            setTimeout(() => {
                copyBtn.textContent = '📋 复制链接';
                copyBtn.style.background = 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)';
            }, 2000);
            
            console.log('📋 支付链接已复制到剪贴板');
        } catch (err) {
            console.error('复制失败:', err);
            copyBtn.textContent = '❌ 复制失败';
            setTimeout(() => {
                copyBtn.textContent = '📋 复制链接';
            }, 2000);
        }
    }
    
    // 绑定事件
    document.getElementById('copy-url-btn').onclick = copyPaymentUrl;
    document.getElementById('refresh-qr-btn').onclick = generateQRCode;
    document.getElementById('direct-pay-btn').onclick = () => {
        console.log('🚀 直接打开支付链接');
        window.open(paymentUrl, '_blank');
    };
    document.getElementById('close-modal-btn').onclick = () => {
        console.log('❌ 关闭弹窗');
        document.getElementById('quick-bank-qr-modal').remove();
    };
    
    // 点击背景关闭
    document.getElementById('quick-bank-qr-modal').onclick = function(e) {
        if (e.target === this) {
            this.remove();
        }
    };
    
    // 按钮悬停效果
    const buttons = document.querySelectorAll('#quick-bank-qr-modal button');
    buttons.forEach(button => {
        button.onmouseenter = function() {
            this.style.transform = 'translateY(-2px)';
        };
        button.onmouseleave = function() {
            this.style.transform = 'translateY(0)';
        };
    });
    
    // 立即生成二维码
    generateQRCode();
    
    console.log('🎉 快速银行支付二维码系统启动完成！');
    
    return {
        success: true,
        message: '快速银行支付二维码系统已启动',
        orderInfo: orderInfo,
        paymentUrl: paymentUrl
    };
    
})();
