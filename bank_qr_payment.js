// 银行网银支付二维码生成脚本
// 用于将网银支付流程改为二维码支付

(function() {
    'use strict';
    
    // 配置参数
    const CONFIG = {
        // 二维码容器ID
        qrContainerId: 'bank-qr-container',
        // 支付金额
        payAmount: '74.15'
    };
    
    // 获取当前支付参数
    function getPaymentParams() {
        const form = document.querySelector('form[action*="bankConfirm"]');
        if (!form) return null;
        
        const input = form.querySelector('input[name="bankPayRequestStr"]');
        if (!input) return null;
        
        try {
            const payData = JSON.parse(input.value);
            return {
                orderId: payData.orderId,
                payAmount: payData.payingChannel.payAmount,
                bankCode: payData.payingChannel.bankCode,
                cardType: payData.payingChannel.cardType,
                channelSign: payData.payingChannel.channelSign,
                agencyCode: payData.payingChannel.agencyCode,
                paySign: payData.paySign,
                pageId: payData.pageId
            };
        } catch (e) {
            console.error('解析支付参数失败:', e);
            return null;
        }
    }
    
    // 使用多个可靠的二维码生成服务
    function generateRealQRCode(text) {
        // 多个备用的二维码生成服务
        const qrServices = [
            `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodeURIComponent(text)}`,
            `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`,
            `https://qr.liantu.com/api.php?text=${encodeURIComponent(text)}&w=200`,
            `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(text)}&size=medium`
        ];

        return qrServices;
    }

    // 生成支付二维码
    function generatePaymentQRCode(params) {
        // 构建支付URL
        const paymentUrl = `https://payc.m.jd.com/qr-pay?` +
            `orderId=${params.orderId}&` +
            `amount=${params.payAmount}&` +
            `bank=${params.bankCode}&` +
            `type=${params.cardType}&` +
            `sign=${params.channelSign}&` +
            `agency=${params.agencyCode}&` +
            `pageId=${params.pageId}`;

        // 获取二维码服务列表
        const qrServices = generateRealQRCode(paymentUrl);

        return {
            paymentUrl: paymentUrl,
            qrCodeUrl: qrServices[0], // 使用第一个服务
            qrServices: qrServices    // 保存所有服务用于备用
        };
    }
    
    // 创建二维码支付界面
    function createQRPaymentInterface(qrData, params) {
        // 创建二维码容器
        const container = document.createElement('div');
        container.id = CONFIG.qrContainerId;
        container.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #e3393c;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            font-family: Arial, sans-serif;
            min-width: 350px;
        `;
        
        // 创建内容
        container.innerHTML = `
            <div style="margin-bottom: 20px;">
                <h3 style="color: #e3393c; margin: 0 0 10px 0;">银行扫码支付</h3>
                <p style="margin: 0; color: #666;">请使用手机银行APP扫描下方二维码完成支付</p>
            </div>
            
            <div style="margin: 20px 0;">
                <img src="${qrData.qrCodeUrl}" alt="支付二维码" style="border: 1px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px;">
                <p style="margin: 5px 0; font-size: 14px;"><strong>订单号：</strong>${params.orderId}</p>
                <p style="margin: 5px 0; font-size: 14px;"><strong>支付金额：</strong>¥${params.payAmount}</p>
                <p style="margin: 5px 0; font-size: 14px;"><strong>支付银行：</strong>${getBankName(params.bankCode)}</p>
                <p style="margin: 5px 0; font-size: 12px; color: #999;">请在5分钟内完成支付</p>
            </div>
            
            <div style="margin-top: 20px;">
                <button id="refresh-qr" style="
                    background: #e3393c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-right: 10px;
                    font-size: 14px;
                ">刷新二维码</button>
                
                <button id="close-qr" style="
                    background: #ccc;
                    color: #333;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                ">关闭</button>
            </div>
            
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                <p>支付完成后页面将自动跳转</p>
                <p>如遇问题请联系客服</p>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(container);
        
        // 添加事件监听
        document.getElementById('refresh-qr').addEventListener('click', function() {
            const newQrData = generatePaymentQRCode(params);
            const img = container.querySelector('img');
            img.src = newQrData.qrCodeUrl;
        });
        
        document.getElementById('close-qr').addEventListener('click', function() {
            container.remove();
        });
        
        // 添加遮罩层
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        `;
        document.body.appendChild(overlay);
        
        // 点击遮罩关闭
        overlay.addEventListener('click', function() {
            container.remove();
            overlay.remove();
        });
        
        return container;
    }
    
    // 获取银行名称
    function getBankName(bankCode) {
        const bankNames = {
            'CCB': '中国建设银行',
            'ICBC': '中国工商银行',
            'ABC': '中国农业银行',
            'BOC': '中国银行',
            'BCOM': '交通银行',
            'CMB': '招商银行',
            'CITIC': '中信银行',
            'CEB': '光大银行',
            'SPDB': '浦发银行',
            'PAB': '平安银行',
            'CMBC': '民生银行',
            'GDB': '广发银行',
            'HXB': '华夏银行',
            'PSBC': '邮储银行'
        };
        return bankNames[bankCode] || '银行';
    }

    // 拦截网银支付按钮点击
    function interceptBankPayment() {
        const payButton = document.querySelector('[onclick*="跳转网银"], .bank-pay-btn, #bank-pay-btn');
        const payButtonText = document.querySelector('div[cursor="pointer"]:contains("跳转网银并支付")');

        // 查找包含"跳转网银并支付"文本的元素
        const allElements = document.querySelectorAll('*');
        let bankPayButton = null;

        for (let element of allElements) {
            if (element.textContent && element.textContent.trim() === '跳转网银并支付') {
                bankPayButton = element;
                break;
            }
        }

        if (bankPayButton) {
            // 移除原有的点击事件
            bankPayButton.onclick = null;

            // 添加新的点击事件
            bankPayButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 获取支付参数
                const params = getPaymentParams();
                if (!params) {
                    alert('获取支付参数失败，请刷新页面重试');
                    return;
                }

                // 生成二维码
                const qrData = generatePaymentQRCode(params);

                // 显示二维码支付界面
                createQRPaymentInterface(qrData, params);

                // 开始轮询支付状态
                startPaymentStatusPolling(params);
            });

            // 修改按钮文本
            bankPayButton.textContent = '生成支付二维码';
            bankPayButton.style.background = '#e3393c';
            bankPayButton.style.color = 'white';
        }
    }

    // 轮询支付状态
    function startPaymentStatusPolling(params) {
        let pollCount = 0;
        const maxPolls = 300; // 5分钟，每秒轮询一次

        const pollInterval = setInterval(function() {
            pollCount++;

            // 检查支付状态
            checkPaymentStatus(params).then(function(status) {
                if (status.paid) {
                    clearInterval(pollInterval);
                    // 支付成功，跳转到成功页面
                    window.location.href = status.successUrl || '/order/success';
                } else if (status.failed) {
                    clearInterval(pollInterval);
                    alert('支付失败：' + status.message);
                    // 关闭二维码窗口
                    const container = document.getElementById(CONFIG.qrContainerId);
                    if (container) container.remove();
                }
            }).catch(function(error) {
                console.error('检查支付状态失败:', error);
            });

            // 超时处理
            if (pollCount >= maxPolls) {
                clearInterval(pollInterval);
                alert('支付超时，请重新尝试');
                const container = document.getElementById(CONFIG.qrContainerId);
                if (container) container.remove();
            }
        }, 1000);
    }

    // 检查支付状态
    function checkPaymentStatus(params) {
        return new Promise(function(resolve, reject) {
            // 这里应该调用实际的支付状态查询接口
            // 由于我们无法访问真实的接口，这里模拟一个检查

            fetch('/api/payment/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    orderId: params.orderId,
                    pageId: params.pageId
                })
            })
            .then(response => response.json())
            .then(data => {
                resolve({
                    paid: data.status === 'success',
                    failed: data.status === 'failed',
                    message: data.message,
                    successUrl: data.successUrl
                });
            })
            .catch(error => {
                // 如果接口调用失败，返回未支付状态
                resolve({
                    paid: false,
                    failed: false,
                    message: '检查中...'
                });
            });
        });
    }

    // 初始化脚本
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(interceptBankPayment, 1000);
            });
        } else {
            setTimeout(interceptBankPayment, 1000);
        }

        console.log('银行二维码支付脚本已加载');
    }

    // 启动脚本
    init();

})();
